{"name": "@workspace/ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.475.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.5", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@turbo/gen": "^2.5.5", "@types/node": "^20.19.9", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}