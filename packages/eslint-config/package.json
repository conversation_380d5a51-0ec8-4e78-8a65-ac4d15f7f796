{"name": "@workspace/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@next/eslint-plugin-next": "^15.4.3", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.31.0", "eslint-config-prettier": "^9.1.2", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.5.5", "globals": "^15.15.0", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}}