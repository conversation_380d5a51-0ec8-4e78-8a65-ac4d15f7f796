// app/login/page.tsx

import SetPassword from "@workspace/ui/components/SetPassword";
import ThemeToggleButton from "@workspace/ui/components/theme-toggle-button";
import Image from "next/image";

export default function LoginPage() {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-background">
      <div className="absolute top-0 right-0 p-4">
        <ThemeToggleButton start="top-right" />
      </div>

      <div className="flex flex-row justify-center max-w-6xl w-full space-y-8 p-8 bg-card rounded-lg shadow-lg border">
        <div>
          <Image
            width={500}
            height={500}
            src="/packages/assets/images/signin_img.jpg"
            alt="Logo"
          ></Image>
        </div>
        <div className="space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-foreground">Sign In</h2>
            <p className="mt-2 text-muted-foreground">
              Welcome back! Please sign in to your account.
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Email
              </label>
              <input
                type="email"
                className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Enter your email"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Password
              </label>
              <SetPassword />
            </div>

            <button className="w-full bg-primary text-primary-foreground py-2 px-4 rounded-md hover:bg-primary/90 transition-colors">
              Sign In
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
